# Kenyan Payroll Management System - Dependencies
# Version 2.0.0 - Updated January 2024
# Python 3.8+ required

# ===== CORE FRAMEWORK =====
Django==4.2.7
djangorestframework==3.14.0

# ===== DATABASE =====
# SQLite included with Python (development default)
# For production, uncomment appropriate database adapter:
# psycopg2-binary==2.9.9  # PostgreSQL (recommended for production)
# mysqlclient==2.2.0      # MySQL/MariaDB

# ===== PDF GENERATION & REPORTS =====
reportlab==4.0.4
weasyprint==60.2

# ===== EXCEL OPERATIONS =====
openpyxl==3.1.2
xlsxwriter==3.1.9
pandas==2.1.3
xlrd==2.0.1

# ===== IMAGE PROCESSING =====
Pillow==10.1.0

# ===== UTILITIES =====
python-dateutil==2.8.2
pytz==2023.3
requests==2.31.0
six==1.16.0

# ===== SECURITY & AUTHENTICATION =====
django-cors-headers==4.3.1
django-csp==3.7
cryptography==41.0.7

# ===== FORMS & UI =====
django-crispy-forms==2.1
crispy-bootstrap5==0.7

# ===== PRODUCTION DEPLOYMENT =====
# Required for Vercel deployment
gunicorn==21.2.0
whitenoise==6.6.0
django-environ==0.11.2
dj-database-url==2.1.0
psycopg2-binary==2.9.9

# ===== DEVELOPMENT TOOLS =====
# Uncomment for development environment
# django-debug-toolbar==4.2.0
# pytest==7.4.3
# pytest-django==4.7.0
# coverage==7.3.2
# factory-boy==3.3.0

# ===== SECURITY ENHANCEMENTS =====
django-ratelimit==4.1.0
django-axes==6.1.1

# ===== CACHING (Optional for performance) =====
# redis==5.0.1
# django-redis==5.4.0

# ===== EMAIL BACKEND (Optional) =====
# django-anymail==10.2

# ===== MONITORING (Optional for production) =====
# sentry-sdk==1.38.0
# django-extensions==3.2.3

# ===== API DOCUMENTATION =====
# drf-spectacular==0.26.5

# ===== CLOUD STORAGE (Optional) =====
# django-storages==1.14.2
# boto3==1.34.0  # For AWS S3

# ===== BACKGROUND TASKS (Optional) =====
# celery==5.3.4
# django-celery-beat==2.5.0
# django-celery-results==2.5.1

# ===== DATA VALIDATION =====
# django-phonenumber-field==7.2.0
# phonenumbers==8.13.26

# ===== BACKUP & HEALTH CHECKS =====
# django-dbbackup==4.0.2
# django-health-check==3.17.0

# ===== TESTING TOOLS =====
# selenium==4.15.2
# django-selenium==0.9.9

# ===== DEVELOPMENT FORMATTING =====
# black==23.11.0
# flake8==6.1.0
# isort==5.12.0
# pre-commit==3.5.0

# ===== DOCUMENTATION =====
# sphinx==7.2.6
# sphinx-rtd-theme==1.3.0

# ===== MINIMUM SECURITY VERSIONS =====
# Ensure these minimum versions for security patches
# urllib3>=1.26.18
# certifi>=2023.7.22
# setuptools>=65.5.1


