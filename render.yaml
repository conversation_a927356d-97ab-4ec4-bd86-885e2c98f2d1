services:
  - type: web
    name: kenyan-payroll-system
    env: python
    plan: starter
    buildCommand: |
      pip install -r requirements-minimal.txt
      python manage.py collectstatic --noinput
    startCommand: |
      python manage.py migrate
      python manage.py create_production_superuser
      gunicorn payroll.wsgi:application
    envVars:
      - key: SECRET_KEY
        value: zbk3HW6l2TLXtzZRslzrgW5M282yIDVA14ps44mPp6Aq8rCgAJFjre4hPPPC6mpbAJo
      - key: DEBUG
        value: False
      - key: DJANGO_SETTINGS_MODULE
        value: payroll.settings.minimal
      - key: DJANGO_SUPERUSER_USERNAME
        value: admin
      - key: DJANGO_SUPERUSER_EMAIL
        value: <EMAIL>
      - key: DJANGO_SUPERUSER_PASSWORD
        value: PayrollAdmin2024!

  - type: pserv
    name: kenyan-payroll-db
    env: postgresql
    plan: starter
    databaseName: payroll_db
    user: payroll_user
