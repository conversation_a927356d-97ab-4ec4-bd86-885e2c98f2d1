# Railway Environment Variables for Kenyan Payroll Management System
# Copy these variables to your Railway project environment

# Django Configuration
SECRET_KEY=zbk3HW6l2TLXtzZRslzrgW5M282yIDVA14ps44mPp6Aq8rCgAJFjre4hPPPC6mpbAJo
DEBUG=False
DJANGO_SETTINGS_MODULE=payroll.settings.railway

# Database (Railway will auto-provide these)
# DATABASE_URL=postgresql://user:password@host:port/database
# PGDATABASE=railway
# PGUSER=postgres
# PGPASSWORD=your-password
# PGHOST=your-host
# PGPORT=5432

# Automatic Superuser Creation
DJANGO_SUPERUSER_USERNAME=admin
DJANGO_SUPERUSER_EMAIL=<EMAIL>
DJANGO_SUPERUSER_PASSWORD=PayrollAdmin2024!

# Optional: Custom Domain
# CUSTOM_DOMAIN=your-domain.com,www.your-domain.com

# Optional: Email Configuration
# EMAIL_HOST=smtp.gmail.com
# EMAIL_PORT=587
# EMAIL_HOST_USER=<EMAIL>
# EMAIL_HOST_PASSWORD=your-gmail-app-password
# DEFAULT_FROM_EMAIL=<EMAIL>

# Optional: Redis Cache (if you add Redis service)
# REDIS_URL=redis://localhost:6379/0
