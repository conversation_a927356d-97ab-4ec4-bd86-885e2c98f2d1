# Example Environment Variables for Kenyan Payroll Management System
# Copy this file to .env.production and fill in your actual values

# Database Configuration
DATABASE_URL=postgres://username:password@host:port/database?sslmode=require

# Django Configuration
SECRET_KEY=your-super-secret-key-here
DEBUG=False
DJANGO_SETTINGS_MODULE=payroll.settings.production

# Allowed Hosts
ALLOWED_HOSTS=.vercel.app,.now.sh,localhost,127.0.0.1,your-domain.com

# Automatic Superuser Creation
DJANGO_SUPERUSER_USERNAME=admin
DJANGO_SUPERUSER_EMAIL=<EMAIL>
DJANGO_SUPERUSER_PASSWORD=your-secure-password

# Optional: Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-gmail-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Optional: Supabase Configuration (if using Supabase features)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
