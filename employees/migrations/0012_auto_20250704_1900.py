# Generated by Django 3.1.14 on 2025-07-04 16:00

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0011_auto_20250704_1858'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='department',
            options={'ordering': ['organization__name', 'name']},
        ),
        migrations.AddField(
            model_name='department',
            name='organization',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='departments', to='employees.organization'),
        ),
        migrations.AlterUniqueTogether(
            name='department',
            unique_together={('organization', 'code'), ('organization', 'name')},
        ),
        migrations.RemoveField(
            model_name='department',
            name='company',
        ),
        migrations.DeleteModel(
            name='Company',
        ),
    ]
