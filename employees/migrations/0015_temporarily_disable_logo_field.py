# Generated by Django 4.2.7 on 2025-07-06 13:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0014_merge_20250706_1333'),
    ]

    operations = [
        migrations.AlterField(
            model_name='department',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='employee',
            name='employment_type',
            field=models.CharField(choices=[('PERMANENT', 'Permanent Employee'), ('CONTRACT', 'Contract Employee'), ('CASUAL', 'Casual Worker'), ('INTERN', 'Intern')], max_length=10),
        ),
        migrations.AlterField(
            model_name='employee',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='jobtitle',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='organization',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='organization',
            name='logo',
            field=models.CharField(blank=True, help_text='Logo path - temporarily disabled during deployment', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='salarystructure',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
    ]
