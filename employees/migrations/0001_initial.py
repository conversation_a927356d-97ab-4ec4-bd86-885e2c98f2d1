# Generated by Django 3.1.14 on 2025-07-04 07:45

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_number', models.CharField(max_length=20, unique=True)),
                ('first_name', models.CharField(max_length=50)),
                ('middle_name', models.CharField(blank=True, max_length=50)),
                ('last_name', models.CharField(max_length=50)),
                ('date_of_birth', models.DateField()),
                ('gender', models.CharField(choices=[('M', 'Male'), ('F', 'Female'), ('O', 'Other')], max_length=1)),
                ('marital_status', models.CharField(choices=[('SINGLE', 'Single'), ('MARRIED', 'Married'), ('DIVORCED', 'Divorced'), ('WIDOWED', 'Widowed')], max_length=10)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('phone_number', models.CharField(max_length=15)),
                ('address', models.TextField()),
                ('employment_type', models.CharField(choices=[('PERMANENT', 'Permanent'), ('CONTRACT', 'Contract'), ('CASUAL', 'Casual'), ('INTERN', 'Intern')], max_length=10)),
                ('date_hired', models.DateField()),
                ('date_terminated', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('kra_pin', models.CharField(help_text='KRA PIN in format: A123456789B', max_length=11, unique=True, validators=[django.core.validators.RegexValidator(message='KRA PIN must be in format: A123456789B', regex='^[A-Z]\\d{9}[A-Z]$')])),
                ('nssf_number', models.CharField(blank=True, max_length=20)),
                ('nhif_number', models.CharField(blank=True, max_length=20)),
                ('bank_name', models.CharField(max_length=100)),
                ('bank_branch', models.CharField(max_length=100)),
                ('account_number', models.CharField(max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='employees.department')),
            ],
            options={
                'ordering': ['employee_number'],
            },
        ),
        migrations.CreateModel(
            name='JobTitle',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['title'],
            },
        ),
        migrations.CreateModel(
            name='SalaryStructure',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('basic_salary', models.DecimalField(decimal_places=2, max_digits=12)),
                ('house_allowance', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('transport_allowance', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('medical_allowance', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('lunch_allowance', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('communication_allowance', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('other_allowances', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('car_benefit_value', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('housing_benefit_value', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('life_insurance_premium', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('health_insurance_premium', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('education_insurance_premium', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('mortgage_interest', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('post_retirement_medical_fund', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('pension_contribution', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('effective_date', models.DateField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='salary_structure', to='employees.employee')),
            ],
            options={
                'ordering': ['-effective_date'],
            },
        ),
        migrations.AddField(
            model_name='employee',
            name='job_title',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='employees.jobtitle'),
        ),
        migrations.AddField(
            model_name='employee',
            name='user',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
    ]
