# Generated by Django 3.1.14 on 2025-07-04 15:25

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


def create_default_company(apps, schema_editor):
    """Create a default company for existing data"""
    Company = apps.get_model('employees', 'Company')

    # Create default company if it doesn't exist
    if not Company.objects.exists():
        Company.objects.create(
            id=1,
            name="Your Company Name",
            trading_name="",
            address_line_1="Company Address Line 1",
            address_line_2="",
            city="Nairobi",
            postal_code="00100",
            country="Kenya",
            phone_number="+254 XXX XXX XXX",
            email="<EMAIL>",
            website="",
            kra_pin="P000000000A",
            business_registration_number="REG000000",
            nssf_employer_number="",
            shif_employer_number="",
            default_pay_day=25,
            is_active=True,
        )


def reverse_create_default_company(apps, schema_editor):
    """Reverse the creation of default company"""
    Company = apps.get_model('employees', 'Company')
    Company.objects.filter(id=1).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0009_make_date_hired_optional'),
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Official company name', max_length=200)),
                ('trading_name', models.CharField(blank=True, help_text='Trading or business name (if different)', max_length=200)),
                ('address_line_1', models.CharField(max_length=100)),
                ('address_line_2', models.CharField(blank=True, max_length=100)),
                ('city', models.CharField(max_length=50)),
                ('postal_code', models.CharField(blank=True, max_length=10)),
                ('country', models.CharField(default='Kenya', max_length=50)),
                ('phone_number', models.CharField(max_length=20)),
                ('email', models.EmailField(max_length=254)),
                ('website', models.URLField(blank=True)),
                ('kra_pin', models.CharField(help_text='Company KRA PIN in format: P123456789A', max_length=11, unique=True, validators=[django.core.validators.RegexValidator(message='Company KRA PIN must be in format: P123456789A', regex='^P\\d{9}[A-Z]$')])),
                ('business_registration_number', models.CharField(help_text='Business registration number from Registrar of Companies', max_length=20, unique=True)),
                ('nssf_employer_number', models.CharField(blank=True, help_text='NSSF employer registration number', max_length=20)),
                ('shif_employer_number', models.CharField(blank=True, help_text='SHIF employer registration number', max_length=20)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='company_logos/')),
                ('default_pay_day', models.IntegerField(default=25, help_text='Default day of month for salary payments (1-31)')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Companies',
                'ordering': ['name'],
            },
        ),
        migrations.RunPython(create_default_company, reverse_create_default_company),
        migrations.AlterModelOptions(
            name='department',
            options={'ordering': ['company__name', 'name']},
        ),
        migrations.AlterField(
            model_name='department',
            name='code',
            field=models.CharField(max_length=10),
        ),
        migrations.AlterField(
            model_name='department',
            name='name',
            field=models.CharField(max_length=100),
        ),
        migrations.AddField(
            model_name='department',
            name='company',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='departments', to='employees.company'),
            preserve_default=False,
        ),
        migrations.AlterUniqueTogether(
            name='department',
            unique_together={('company', 'code'), ('company', 'name')},
        ),
    ]
