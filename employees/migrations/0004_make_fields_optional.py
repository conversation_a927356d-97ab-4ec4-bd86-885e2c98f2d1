# Generated by Django 3.1.14 on 2025-07-04 09:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0003_add_national_id_nullable'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='employee',
            name='address',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='employee',
            name='date_of_birth',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='employee',
            name='marital_status',
            field=models.CharField(blank=True, choices=[('SINGLE', 'Single'), ('MARRIED', 'Married'), ('DIVORCED', 'Divorced'), ('WIDOWED', 'Widowed')], max_length=10),
        ),
    ]
