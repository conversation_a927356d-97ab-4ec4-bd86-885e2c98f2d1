# Generated by Django 3.1.14 on 2025-07-04 15:58

from django.db import migrations, models
import django.core.validators


def create_default_organization(apps, schema_editor):
    """Create a default organization"""
    Organization = apps.get_model('employees', 'Organization')

    # Create default organization
    Organization.objects.create(
        id=1,
        organization_type='COMPANY',
        name='Your Organization Name',
        short_name='',
        trading_name='',
        ministry='',
        sector='',
        address_line_1='Organization Address Line 1',
        address_line_2='',
        city='Nairobi',
        postal_code='00100',
        country='Kenya',
        phone_number='+254 XXX XXX XXX',
        email='<EMAIL>',
        website='',
        kra_pin='P000000000A',
        registration_number='REG000000',
        registration_authority='Registrar of Companies',
        nssf_employer_number='',
        shif_employer_number='',
        default_pay_day=25,
        is_active=True,
    )


def reverse_create_default_organization(apps, schema_editor):
    """Reverse the creation of default organization"""
    Organization = apps.get_model('employees', 'Organization')
    Organization.objects.filter(id=1).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0010_auto_20250704_1825'),
    ]

    operations = [
        # Create Organization model
        migrations.CreateModel(
            name='Organization',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('organization_type', models.CharField(choices=[('COMPANY', 'Private Company'), ('GOVERNMENT', 'Government Entity'), ('PARASTATAL', 'Parastatal/State Corporation'), ('NGO', 'Non-Governmental Organization'), ('INTERNATIONAL', 'International Organization'), ('COOPERATIVE', 'Cooperative Society'), ('INSTITUTION', 'Educational/Training Institution'), ('RELIGIOUS', 'Religious Organization'), ('OTHER', 'Other Organization')], default='COMPANY', help_text='Type of organization', max_length=20)),
                ('name', models.CharField(help_text='Official organization name', max_length=200)),
                ('short_name', models.CharField(blank=True, help_text='Short name or acronym (e.g., KRA, NHIF)', max_length=100)),
                ('trading_name', models.CharField(blank=True, help_text='Trading or business name (if different)', max_length=200)),
                ('ministry', models.CharField(blank=True, help_text='Parent ministry (for government entities)', max_length=200)),
                ('sector', models.CharField(blank=True, help_text='Sector/Industry (e.g., Health, Education, Finance)', max_length=100)),
                ('address_line_1', models.CharField(max_length=100)),
                ('address_line_2', models.CharField(blank=True, max_length=100)),
                ('city', models.CharField(max_length=50)),
                ('postal_code', models.CharField(blank=True, max_length=10)),
                ('country', models.CharField(default='Kenya', max_length=50)),
                ('phone_number', models.CharField(max_length=20)),
                ('email', models.EmailField(max_length=254)),
                ('website', models.URLField(blank=True)),
                ('kra_pin', models.CharField(help_text='Organization KRA PIN in format: P123456789A', max_length=11, unique=True, validators=[django.core.validators.RegexValidator(message='Organization KRA PIN must be in format: P123456789A', regex='^P\\d{9}[A-Z]$')])),
                ('registration_number', models.CharField(blank=True, help_text='Registration number (Business/Government/NGO registration)', max_length=50)),
                ('registration_authority', models.CharField(blank=True, help_text='Registration authority (e.g., Registrar of Companies, NGO Board)', max_length=100)),
                ('nssf_employer_number', models.CharField(blank=True, help_text='NSSF employer registration number', max_length=20)),
                ('shif_employer_number', models.CharField(blank=True, help_text='SHIF employer registration number', max_length=20)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='company_logos/')),
                ('default_pay_day', models.IntegerField(default=25, help_text='Default day of month for salary payments (1-31)')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Organizations',
                'ordering': ['organization_type', 'name'],
            },
        ),

        # Create default organization
        migrations.RunPython(create_default_organization, reverse_create_default_organization),
    ]
