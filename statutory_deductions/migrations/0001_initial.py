# Generated by Django 3.1.14 on 2025-07-04 07:45

from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AffordableHousingLevyRate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_rate', models.DecimalField(decimal_places=2, default=Decimal('1.5'), max_digits=5)),
                ('employer_rate', models.DecimalField(decimal_places=2, default=Decimal('1.5'), max_digits=5)),
                ('effective_date', models.DateField()),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'ordering': ['-effective_date'],
            },
        ),
        migrations.CreateModel(
            name='NHIFRate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lower_limit', models.DecimalField(decimal_places=2, max_digits=12)),
                ('upper_limit', models.DecimalField(decimal_places=2, max_digits=12)),
                ('contribution_amount', models.DecimalField(decimal_places=2, max_digits=8)),
                ('effective_date', models.DateField()),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'ordering': ['lower_limit'],
            },
        ),
        migrations.CreateModel(
            name='NSSFRate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tier', models.IntegerField(choices=[(1, 'Tier 1'), (2, 'Tier 2')])),
                ('lower_limit', models.DecimalField(decimal_places=2, max_digits=12)),
                ('upper_limit', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('contribution_rate', models.DecimalField(decimal_places=2, max_digits=5)),
                ('effective_date', models.DateField()),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'ordering': ['tier', 'lower_limit'],
            },
        ),
        migrations.CreateModel(
            name='PAYETaxBand',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lower_limit', models.DecimalField(decimal_places=2, max_digits=12)),
                ('upper_limit', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('tax_rate', models.DecimalField(decimal_places=2, max_digits=5)),
                ('effective_date', models.DateField()),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'ordering': ['lower_limit'],
            },
        ),
        migrations.CreateModel(
            name='SHIFRate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('contribution_rate', models.DecimalField(decimal_places=2, default=Decimal('2.75'), max_digits=5)),
                ('minimum_contribution', models.DecimalField(decimal_places=2, default=Decimal('300'), max_digits=8)),
                ('effective_date', models.DateField()),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'ordering': ['-effective_date'],
            },
        ),
        migrations.CreateModel(
            name='TaxRelief',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('relief_type', models.CharField(choices=[('PERSONAL', 'Personal Relief'), ('INSURANCE', 'Insurance Relief'), ('MORTGAGE', 'Mortgage Interest Relief'), ('PENSION', 'Pension Contribution Relief'), ('MEDICAL_FUND', 'Post-Retirement Medical Fund Relief')], max_length=20)),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('rate', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('maximum_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('effective_date', models.DateField()),
                ('is_active', models.BooleanField(default=True)),
                ('description', models.TextField(blank=True)),
            ],
            options={
                'ordering': ['relief_type', '-effective_date'],
            },
        ),
    ]
