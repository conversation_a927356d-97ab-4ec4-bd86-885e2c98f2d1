<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#0d6efd">
    <meta name="format-detection" content="telephone=no">
    <title>{% block title %}Kenyan Payroll Management System{% endblock %}</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    {% load static %}
    <link href="{% static 'css/custom.css' %}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="{% url 'core:dashboard' %}">
                <i class="bi bi-calculator me-2"></i>
                <span class="d-none d-sm-inline">Kenyan Payroll System</span>
                <span class="d-sm-none">Payroll</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'employees:employee_list' %}">
                            <i class="bi bi-people me-1"></i>Employees
                            {% if user.is_staff %}
                                <small class="badge bg-warning text-dark ms-1">Admin+</small>
                            {% endif %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'payroll_processing:payroll_calculator' %}">
                            <i class="bi bi-calculator me-1"></i>
                            <span class="d-lg-inline">Payroll Calculator</span>
                            <span class="d-none d-md-inline d-lg-none">Calculator</span>
                            <span class="d-md-none">Payroll</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'payroll_processing:tax_calculator' %}">
                            <i class="bi bi-percent me-1"></i>
                            <span class="d-lg-inline">Tax Calculator</span>
                            <span class="d-none d-md-inline d-lg-none">Tax</span>
                            <span class="d-md-none">Tax</span>
                        </a>
                    </li>
                    {% if user.is_staff %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'payroll_processing:payroll_generation' %}">
                                <i class="bi bi-calendar-check me-1"></i>Generate Payroll
                                <small class="badge bg-warning text-dark ms-1">Admin</small>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'payroll_processing:payroll_periods' %}">
                                <i class="bi bi-calendar-range me-1"></i>Payroll Periods
                                <small class="badge bg-warning text-dark ms-1">Admin</small>
                            </a>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <span class="nav-link text-muted" title="Admin access required">
                                <i class="bi bi-lock me-1"></i>Generate Payroll
                                <small class="badge bg-secondary ms-1">Admin Only</small>
                            </span>
                        </li>
                        <li class="nav-item">
                            <span class="nav-link text-muted" title="Admin access required">
                                <i class="bi bi-lock me-1"></i>Payroll Periods
                                <small class="badge bg-secondary ms-1">Admin Only</small>
                            </span>
                        </li>
                    {% endif %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'payroll_processing:payroll_reports' %}">
                            <i class="bi bi-graph-up me-1"></i>Reports
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i>{{ user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/admin/"><i class="bi bi-gear me-2"></i>Admin</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'logout' %}"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'login' %}">
                                <i class="bi bi-box-arrow-in-right me-1"></i>Login
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        <!-- Messages -->
        {% if messages %}
            <div class="row">
                <div class="col-12">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}

        <!-- Page Header -->
        {% block page_header %}{% endblock %}

        <!-- Page Content -->
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5 no-print">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="fw-bold text-primary">Kenyan Payroll Management System</h6>
                    <p class="text-muted small mb-0">
                        Compliant with KRA regulations, NSSF, SHIF, and Affordable Housing Levy requirements.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted small mb-0">
                        <i class="bi bi-calendar me-1"></i>
                        Built for Kenya's employment structure
                    </p>
                    <p class="text-muted small mb-0">
                        <i class="bi bi-shield-check me-1"></i>
                        SHIF-compliant (replaced NHIF)
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{% static 'js/custom.js' %}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
