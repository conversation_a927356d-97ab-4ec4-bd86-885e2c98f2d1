{% extends 'base/base.html' %}
{% load static %}

{% block title %}Deactivate {{ employee.full_name }} - Kenyan Payroll Management System{% endblock %}

{% block page_header %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="display-6 fw-bold text-danger mb-2">
                    <i class="bi bi-person-x me-3"></i>
                    {{ title }}
                </h1>
                <p class="lead text-muted">
                    Confirm employee deactivation
                </p>
            </div>
            <a href="{% url 'employees:employee_detail' employee.pk %}" class="btn btn-outline-primary">
                <i class="bi bi-arrow-left me-2"></i>Back to Employee
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header bg-warning">
                <h5 class="mb-0">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Confirm Deactivation
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>Warning:</strong> This action will deactivate the employee record. The employee will no longer appear in active employee lists and cannot be processed for payroll.
                </div>
                
                <div class="mb-4">
                    <h6 class="text-primary">Employee Details</h6>
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Employee Number:</strong></td>
                            <td>{{ employee.employee_number }}</td>
                        </tr>
                        <tr>
                            <td><strong>Full Name:</strong></td>
                            <td>{{ employee.full_name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Department:</strong></td>
                            <td>{{ employee.department.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Job Title:</strong></td>
                            <td>{{ employee.job_title.title }}</td>
                        </tr>
                        <tr>
                            <td><strong>Date Hired:</strong></td>
                            <td>{{ employee.date_hired|date:"M d, Y" }}</td>
                        </tr>
                    </table>
                </div>
                
                <p class="text-muted">
                    Are you sure you want to deactivate <strong>{{ employee.full_name }}</strong>? 
                    This action can be reversed later by editing the employee record.
                </p>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'employees:employee_detail' employee.pk %}" class="btn btn-outline-secondary btn-lg me-md-2">
                            <i class="bi bi-x-circle me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-danger btn-lg">
                            <i class="bi bi-person-x me-2"></i>Deactivate Employee
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
