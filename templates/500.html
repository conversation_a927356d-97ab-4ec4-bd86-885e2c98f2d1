<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Error - Kenyan Payroll Management System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 text-center">
                <div class="py-5">
                    <div class="mb-4">
                        <i class="bi bi-exclamation-circle-fill text-danger" style="font-size: 5rem;"></i>
                    </div>
                    <h1 class="display-4 text-muted">500</h1>
                    <h2 class="mb-3">Server Error</h2>
                    <p class="lead text-muted mb-4">
                        We're experiencing some technical difficulties. Our team has been notified and is working to resolve the issue.
                    </p>
                    <div class="d-grid gap-2 d-md-block">
                        <a href="/" class="btn btn-primary">
                            <i class="bi bi-house me-2"></i>Go to Home
                        </a>
                        <a href="javascript:history.back()" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-2"></i>Go Back
                        </a>
                    </div>
                    <div class="mt-4">
                        <small class="text-muted">
                            If this problem persists, please contact your system administrator.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
