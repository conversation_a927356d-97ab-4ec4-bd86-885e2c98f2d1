{% extends 'base/base.html' %}
{% load static %}

{% block title %}Payroll Calculator - {% if organization %}{{ organization.name }}{% else %}Kenyan Payroll Management System{% endif %}{% endblock %}

{% block page_header %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
            <div class="mb-3 mb-md-0">
                {% if organization %}
                    <div class="d-flex flex-column flex-sm-row align-items-start align-items-sm-center mb-3">
                        {% if organization.logo %}
                            <img src="{{ organization.logo.url }}" alt="{{ organization.name }} Logo"
                                 class="me-0 me-sm-4 mb-2 mb-sm-0" style="height: 60px; width: auto; object-fit: contain;">
                        {% endif %}
                        <div>
                            <h1 class="h3 h2-md fw-bold text-primary mb-1">
                                {{ organization.name }}
                            </h1>
                            <p class="text-muted mb-0 small">
                                Payroll Calculator
                            </p>
                        </div>
                    </div>
                {% else %}
                    <h1 class="h3 h2-md fw-bold text-primary mb-2">
                        <i class="bi bi-calculator me-2"></i>
                        Payroll Calculator
                    </h1>
                    <p class="text-muted small">
                        Calculate Kenyan statutory deductions and net pay
                    </p>
                {% endif %}
            </div>
            <a href="{% url 'employees:employee_list' %}" class="btn btn-outline-primary btn-sm">
                <i class="bi bi-arrow-left me-1"></i>
                <span class="d-none d-sm-inline">Back to </span>Employees
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Calculator Form -->
    <div class="col-12 col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-input-cursor me-2"></i>
                    Salary Information
                </h5>
            </div>
            <div class="card-body">
                <form id="payroll-form">
                    <div class="mb-3">
                        <label for="gross_salary" class="form-label">
                            <i class="bi bi-currency-exchange me-1"></i>
                            Gross Monthly Salary (KES)
                        </label>
                        <input type="number" 
                               class="form-control form-control-lg salary-input" 
                               id="gross_salary" 
                               name="gross_salary"
                               placeholder="Enter gross salary"
                               min="0" 
                               step="0.01"
                               data-bs-toggle="tooltip"
                               title="Enter the employee's gross monthly salary">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="employment_type" class="form-label">
                                <i class="bi bi-person-badge me-1"></i>
                                Employment Type
                            </label>
                            <select class="form-select" id="employment_type" name="employment_type">
                                <option value="PERMANENT">Permanent Employee</option>
                                <option value="CONTRACT">Contract Employee</option>
                                <option value="CASUAL">Casual Worker</option>
                                <option value="INTERN">Intern</option>
                            </select>
                            <div class="form-text">
                                <small class="text-info">
                                    <i class="bi bi-info-circle me-1"></i>
                                    Contract employees are exempt from NSSF and Housing Levy
                                </small>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="basic_salary" class="form-label">Basic Salary (KES)</label>
                            <input type="number"
                                   class="form-control"
                                   id="basic_salary"
                                   name="basic_salary"
                                   placeholder="Basic salary"
                                   min="0"
                                   step="0.01">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="allowances" class="form-label">Total Allowances (KES)</label>
                            <input type="number"
                                   class="form-control"
                                   id="allowances"
                                   name="allowances"
                                   placeholder="Allowances"
                                   min="0"
                                   step="0.01">
                        </div>
                    </div>
                    
                    <!-- Advanced Options -->
                    <div class="accordion mb-3" id="advancedOptions">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseAdvanced">
                                    <i class="bi bi-gear me-2"></i>
                                    Advanced Options (Tax Relief)
                                </button>
                            </h2>
                            <div id="collapseAdvanced" class="accordion-collapse collapse" data-bs-parent="#advancedOptions">
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="insurance_premiums" class="form-label">
                                                Insurance Premiums (KES)
                                                <i class="bi bi-info-circle" 
                                                   data-bs-toggle="tooltip" 
                                                   title="Life, health, or education insurance premiums (15% relief, max KES 60,000/year)"></i>
                                            </label>
                                            <input type="number" 
                                                   class="form-control" 
                                                   id="insurance_premiums" 
                                                   name="insurance_premiums"
                                                   placeholder="Monthly premiums"
                                                   min="0" 
                                                   step="0.01">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="mortgage_interest" class="form-label">
                                                Mortgage Interest (KES)
                                                <i class="bi bi-info-circle" 
                                                   data-bs-toggle="tooltip" 
                                                   title="Monthly mortgage interest (max KES 30,000/month)"></i>
                                            </label>
                                            <input type="number" 
                                                   class="form-control" 
                                                   id="mortgage_interest" 
                                                   name="mortgage_interest"
                                                   placeholder="Monthly interest"
                                                   min="0" 
                                                   step="0.01">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="pension_contribution" class="form-label">
                                                Pension Contribution (KES)
                                                <i class="bi bi-info-circle" 
                                                   data-bs-toggle="tooltip" 
                                                   title="Monthly pension contribution (max KES 30,000/month)"></i>
                                            </label>
                                            <input type="number" 
                                                   class="form-control" 
                                                   id="pension_contribution" 
                                                   name="pension_contribution"
                                                   placeholder="Monthly contribution"
                                                   min="0" 
                                                   step="0.01">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="medical_fund" class="form-label">
                                                Post-Retirement Medical Fund (KES)
                                                <i class="bi bi-info-circle" 
                                                   data-bs-toggle="tooltip" 
                                                   title="Monthly contribution to post-retirement medical fund (max KES 15,000/month)"></i>
                                            </label>
                                            <input type="number" 
                                                   class="form-control" 
                                                   id="medical_fund" 
                                                   name="medical_fund"
                                                   placeholder="Monthly contribution"
                                                   min="0" 
                                                   step="0.01">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary btn-lg" onclick="calculatePayroll()">
                            <i class="bi bi-calculator me-2"></i>
                            Calculate Payroll
                        </button>
                        <button type="reset" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise me-2"></i>
                            Reset Form
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Results -->
    <div class="col-12 col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-file-earmark-text me-2"></i>
                    Calculation Results
                </h5>
            </div>
            <div class="card-body">
                <div id="calculation-results">
                    <div class="text-center py-5 text-muted">
                        <i class="bi bi-calculator fs-1 mb-3"></i>
                        <p>Enter a gross salary to see the payroll breakdown</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tax Information -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    Current Tax Rates & Information
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">PAYE Tax Bands</h6>
                        <ul class="list-unstyled small">
                            <li>KES 0 - 24,000: <strong>10%</strong></li>
                            <li>KES 24,001 - 32,333: <strong>25%</strong></li>
                            <li>KES 32,334 - 500,000: <strong>30%</strong></li>
                            <li>KES 500,001 - 800,000: <strong>32.5%</strong></li>
                            <li>KES 800,001+: <strong>35%</strong></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">Statutory Deductions</h6>
                        <ul class="list-unstyled small">
                            <li><strong>NSSF:</strong> 6% (Tier 1 & 2)</li>
                            <li><strong>SHIF:</strong> 2.75% (Min KES 300)</li>
                            <li><strong>Housing Levy:</strong> 1.5% each</li>
                            <li><strong>Personal Relief:</strong> KES 2,400</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-info mt-3 mb-0">
                    <small>
                        <i class="bi bi-lightbulb me-1"></i>
                        <strong>Note:</strong> SHIF replaced NHIF in 2024. This calculator uses the latest rates as per Kenya's tax regulations.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sample Calculations -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    Sample Calculations
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-primary w-100" onclick="loadSample(25000)">
                            <strong>KES 25,000</strong><br>
                            <small>Entry Level</small>
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-primary w-100" onclick="loadSample(50000)">
                            <strong>KES 50,000</strong><br>
                            <small>Mid Level</small>
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-primary w-100" onclick="loadSample(100000)">
                            <strong>KES 100,000</strong><br>
                            <small>Senior Level</small>
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-primary w-100" onclick="loadSample(200000)">
                            <strong>KES 200,000</strong><br>
                            <small>Executive Level</small>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function loadSample(salary) {
    document.getElementById('gross_salary').value = salary;
    calculatePayroll();
}

// Reset form handler
document.querySelector('button[type="reset"]').addEventListener('click', function() {
    clearResults();
});
</script>
{% endblock %}
