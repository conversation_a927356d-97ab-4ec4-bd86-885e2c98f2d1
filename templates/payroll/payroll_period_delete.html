{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }} - {% if organization %}{{ organization.name }}{% else %}Kenyan Payroll Management System{% endif %}{% endblock %}

{% block extra_css %}
<style>
    .delete-warning {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .period-info {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid #dc3545;
    }
    
    .danger-actions {
        background: #fff5f5;
        border: 1px solid #fed7d7;
        border-radius: 8px;
        padding: 1.5rem;
        text-align: center;
    }
    
    .payslip-warning {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block page_header %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                {% if organization and organization.logo %}
                    <img src="{{ organization.logo.url }}" alt="{{ organization.name }} Logo"
                         class="me-4" style="height: 60px; width: auto; object-fit: contain;">
                {% endif %}
                <div>
                    <h1 class="display-6 fw-bold text-danger mb-2">
                        <i class="bi bi-calendar-x me-3"></i>
                        Delete Payroll Period
                    </h1>
                    <p class="lead text-muted">
                        {% if organization %}{{ organization.name }} - {% endif %}Permanently remove payroll period from system
                    </p>
                </div>
            </div>
            <a href="{% url 'payroll_processing:payroll_periods' %}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i>Back to Payroll Periods
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Warning Section -->
<div class="delete-warning">
    <i class="bi bi-exclamation-triangle fs-1 mb-3"></i>
    <h3 class="mb-3">⚠️ CRITICAL WARNING ⚠️</h3>
    <p class="mb-0 fs-5">
        You are about to permanently delete this payroll period and ALL associated payslips.
        <br><strong>This action cannot be undone and will affect payroll records!</strong>
    </p>
</div>

<!-- Payslip Warning -->
{% if payslip_count > 0 %}
<div class="payslip-warning">
    <h6 class="text-warning mb-2">
        <i class="bi bi-exclamation-circle me-2"></i>
        Payslip Impact Warning
    </h6>
    <p class="mb-0">
        This payroll period contains <strong>{{ payslip_count }} payslip(s)</strong> that will also be permanently deleted.
        All employee payroll records for this period will be lost.
    </p>
</div>
{% endif %}

<!-- Period Information -->
<div class="period-info">
    <h5 class="text-danger mb-3">
        <i class="bi bi-calendar-range me-2"></i>
        Payroll Period to be Deleted
    </h5>
    
    <div class="row">
        <div class="col-md-6">
            <table class="table table-borderless">
                <tr>
                    <td><strong>Period Name:</strong></td>
                    <td>{{ period.name }}</td>
                </tr>
                <tr>
                    <td><strong>Period Type:</strong></td>
                    <td>{{ period.period_type }}</td>
                </tr>
                <tr>
                    <td><strong>Start Date:</strong></td>
                    <td>{{ period.start_date|date:"M d, Y" }}</td>
                </tr>
                <tr>
                    <td><strong>End Date:</strong></td>
                    <td>{{ period.end_date|date:"M d, Y" }}</td>
                </tr>
            </table>
        </div>
        <div class="col-md-6">
            <table class="table table-borderless">
                <tr>
                    <td><strong>Pay Date:</strong></td>
                    <td>{{ period.pay_date|date:"M d, Y" }}</td>
                </tr>
                <tr>
                    <td><strong>Status:</strong></td>
                    <td>
                        {% if period.status == 'DRAFT' %}
                            <span class="badge bg-secondary">Draft</span>
                        {% elif period.status == 'PROCESSED' %}
                            <span class="badge bg-success">Processed</span>
                        {% elif period.status == 'APPROVED' %}
                            <span class="badge bg-primary">Approved</span>
                        {% elif period.status == 'PAID' %}
                            <span class="badge bg-info">Paid</span>
                        {% else %}
                            <span class="badge bg-warning">{{ period.status }}</span>
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <td><strong>Total Payslips:</strong></td>
                    <td><span class="badge bg-danger">{{ payslip_count }}</span></td>
                </tr>
                <tr>
                    <td><strong>Created:</strong></td>
                    <td>{{ period.created_at|date:"M d, Y H:i" }}</td>
                </tr>
            </table>
        </div>
    </div>
</div>

<!-- Consequences Warning -->
<div class="alert alert-danger">
    <h6 class="alert-heading">
        <i class="bi bi-exclamation-circle me-2"></i>
        What will be permanently deleted:
    </h6>
    <ul class="mb-0">
        <li>The entire payroll period record</li>
        <li>All {{ payslip_count }} employee payslips for this period</li>
        <li>All salary calculations and deductions data</li>
        <li>All statutory deductions records (PAYE, NSSF, SHIF, Housing Levy)</li>
        <li>All payroll summary and reporting data</li>
        <li>This action is <strong>PERMANENT</strong> and cannot be reversed</li>
    </ul>
</div>

<!-- Action Buttons -->
<div class="danger-actions">
    <h6 class="text-danger mb-3">Confirm Deletion</h6>
    <p class="text-muted mb-4">
        Type the period name to confirm deletion: <strong>{{ period.name }}</strong>
    </p>
    
    <form method="post" id="deleteForm">
        {% csrf_token %}
        <div class="mb-3">
            <input type="text" class="form-control" id="confirmName" 
                   placeholder="Type period name to confirm" 
                   style="max-width: 400px; margin: 0 auto;">
        </div>
        
        <div class="d-flex justify-content-center gap-3">
            <a href="{% url 'payroll_processing:payroll_periods' %}" class="btn btn-secondary btn-lg">
                <i class="bi bi-x-circle me-2"></i>Cancel
            </a>
            <button type="submit" class="btn btn-danger btn-lg" id="deleteBtn" disabled>
                <i class="bi bi-trash me-2"></i>Delete Payroll Period Permanently
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmInput = document.getElementById('confirmName');
    const deleteBtn = document.getElementById('deleteBtn');
    const expectedName = "{{ period.name }}";
    
    confirmInput.addEventListener('input', function() {
        if (this.value.trim() === expectedName) {
            deleteBtn.disabled = false;
            deleteBtn.classList.remove('btn-danger');
            deleteBtn.classList.add('btn-outline-danger');
        } else {
            deleteBtn.disabled = true;
            deleteBtn.classList.remove('btn-outline-danger');
            deleteBtn.classList.add('btn-danger');
        }
    });
    
    document.getElementById('deleteForm').addEventListener('submit', function(e) {
        if (confirmInput.value.trim() !== expectedName) {
            e.preventDefault();
            alert('Please type the period name exactly as shown to confirm deletion.');
            return false;
        }
        
        const payslipCount = {{ payslip_count }};
        const confirmMessage = payslipCount > 0 
            ? `Are you absolutely sure you want to delete this payroll period and ${payslipCount} associated payslips? This action cannot be undone!`
            : 'Are you absolutely sure you want to delete this payroll period? This action cannot be undone!';
        
        if (!confirm(confirmMessage)) {
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}
