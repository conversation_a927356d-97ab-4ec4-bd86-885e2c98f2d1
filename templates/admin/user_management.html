{% extends 'base/base.html' %}
{% load static %}
{% load dict_extras %}

{% block title %}User Management - {% if organization %}{{ organization.name }}{% else %}{{ block.super }}{% endif %}{% endblock %}

{% block extra_css %}
<style>
    .user-card {
        transition: transform 0.2s, box-shadow 0.2s;
        border-left: 4px solid #dee2e6;
    }
    .user-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .user-card.active {
        border-left-color: #28a745;
        background-color: #f8fff9;
    }
    .user-card.inactive {
        border-left-color: #dc3545;
        background-color: #fff5f5;
    }
    .role-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
    }
    .create-user-card {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        color: white;
        border: none;
    }

    .org-header {
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .org-logo {
        height: 70px;
        width: auto;
        object-fit: contain;
        flex-shrink: 0;
    }

    .org-info {
        display: flex;
        flex-direction: column;
        justify-content: center;
        line-height: 1.2;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            {% if organization %}
                <div class="org-header">
                    {% if organization.logo %}
                        <img src="{{ organization.logo.url }}" alt="{{ organization.name }} Logo" class="org-logo">
                    {% endif %}
                    <div class="org-info">
                        <h1 class="fs-1 mb-1">{{ organization.name }}</h1>
                        <p class="text-muted mb-1 fs-5">User Management</p>
                        <p class="text-muted mb-0 fs-6">Manage users and assign roles for platform access</p>
                    </div>
                </div>
            {% else %}
                <h1 class="h3 mb-0">User Management</h1>
                <p class="text-muted">Manage users and assign roles for platform access</p>
            {% endif %}
        </div>
        <div>
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-3">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{% url 'payroll_admin:dashboard' %}">Admin</a></li>
                    <li class="breadcrumb-item active" aria-current="page">User Management</li>
                </ol>
            </nav>
            <!-- Action Buttons -->
            <div class="btn-group">
                <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createUserModal">
                    <i class="bi bi-person-plus me-2"></i>Create User
                </button>
                <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#initializeRolesModal">
                    <i class="bi bi-shield-check me-2"></i>Initialize Roles
                </button>
                <a href="{% url 'payroll_admin:dashboard' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>Back to Admin
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="bi bi-people fs-1 mb-3"></i>
                    <h3 class="mb-0">{{ users.count }}</h3>
                    <p class="mb-0">Total Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="bi bi-person-check fs-1 mb-3"></i>
                    <h3 class="mb-0">{{ users|length }}</h3>
                    <p class="mb-0">Active Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="bi bi-shield-lock fs-1 mb-3"></i>
                    <h3 class="mb-0">{{ roles|length }}</h3>
                    <p class="mb-0">Available Roles</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card create-user-card">
                <div class="card-body text-center">
                    <i class="bi bi-person-plus fs-1 mb-3"></i>
                    <h6>Quick Create</h6>
                    <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#createUserModal">
                        Add User
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Available Roles -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Available Roles</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for role in roles %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <h6 class="card-title text-primary">{{ role.display_name }}</h6>
                                    <p class="card-text small">{{ role.description }}</p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users List -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">System Users</h5>
        </div>
        <div class="card-body">
            {% if users %}
                <div class="row">
                    {% for user in users %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card user-card {% if user.is_active %}active{% else %}inactive{% endif %}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="card-title mb-0">{{ user.get_full_name|default:user.username }}</h6>
                                        <small class="text-muted">@{{ user.username }}</small>
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            <i class="bi bi-three-dots"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <button class="dropdown-item edit-role-btn" 
                                                        data-user-id="{{ user.id }}" 
                                                        data-username="{{ user.username }}"
                                                        data-current-role="{{ user_roles|get_item:user.id }}">
                                                    <i class="bi bi-pencil me-2"></i>Edit Role
                                                </button>
                                            </li>
                                            <li>
                                                <form method="post" action="{% url 'payroll_admin:toggle_user_status' user.id %}" style="display: inline;">
                                                    {% csrf_token %}
                                                    <button type="submit" class="dropdown-item">
                                                        <i class="bi bi-{% if user.is_active %}pause{% else %}play{% endif %} me-2"></i>
                                                        {% if user.is_active %}Deactivate{% else %}Activate{% endif %}
                                                    </button>
                                                </form>
                                            </li>
                                            {% if user != request.user %}
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <button class="dropdown-item text-danger delete-user-btn" 
                                                        data-user-id="{{ user.id }}" 
                                                        data-username="{{ user.username }}">
                                                    <i class="bi bi-trash me-2"></i>Delete User
                                                </button>
                                            </li>
                                            {% endif %}
                                        </ul>
                                    </div>
                                </div>
                                
                                <p class="mb-2">
                                    <i class="bi bi-envelope me-1"></i>{{ user.email }}
                                </p>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge role-badge 
                                        {% if user_roles|get_item:user.id == 'super_admin' %}bg-danger
                                        {% elif user_roles|get_item:user.id == 'admin' %}bg-primary
                                        {% elif user_roles|get_item:user.id == 'hr_manager' %}bg-success
                                        {% elif user_roles|get_item:user.id == 'payroll_officer' %}bg-warning
                                        {% elif user_roles|get_item:user.id == 'employee' %}bg-info
                                        {% else %}bg-secondary{% endif %}">
                                        {{ user_roles|get_item:user.id|default:"No Role"|title }}
                                    </span>
                                    <small class="text-muted">
                                        {% if user.is_active %}
                                            <i class="bi bi-check-circle text-success"></i> Active
                                        {% else %}
                                            <i class="bi bi-x-circle text-danger"></i> Inactive
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-people fs-1 text-muted mb-3"></i>
                    <h5 class="text-muted">No Users Found</h5>
                    <p class="text-muted">Create your first user to get started.</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createUserModal">
                        <i class="bi bi-person-plus me-2"></i>Create User
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Create User Modal -->
<div class="modal fade" id="createUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'payroll_admin:create_user' %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">Username *</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">First Name</label>
                            <input type="text" class="form-control" id="first_name" name="first_name">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">Last Name</label>
                            <input type="text" class="form-control" id="last_name" name="last_name">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">Password *</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">Role *</label>
                            <select class="form-control" id="role" name="role" required>
                                <option value="">Select Role</option>
                                {% for role in roles %}
                                <option value="{{ role.name }}">{{ role.display_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Role Modal -->
<div class="modal fade" id="editRoleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit User Role</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" id="editRoleForm">
                <div class="modal-body">
                    {% csrf_token %}
                    <p>Change role for user: <strong id="editUsername"></strong></p>
                    <div class="mb-3">
                        <label for="editRole" class="form-label">New Role</label>
                        <select class="form-control" id="editRole" name="role" required>
                            {% for role in roles %}
                            <option value="{{ role.name }}">{{ role.display_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Role</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete User Modal -->
<div class="modal fade" id="deleteUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" id="deleteUserForm">
                <div class="modal-body">
                    {% csrf_token %}
                    <p>Are you sure you want to delete user: <strong id="deleteUsername"></strong>?</p>
                    <p class="text-danger small">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Initialize Roles Modal -->
<div class="modal fade" id="initializeRolesModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Initialize System Roles</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'payroll_admin:initialize_roles' %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <p>This will create/update all system roles and their permissions.</p>
                    <p class="text-warning small">This is typically done once during system setup.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Initialize Roles</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Edit role modal
    const editRoleModal = new bootstrap.Modal(document.getElementById('editRoleModal'));
    const editRoleForm = document.getElementById('editRoleForm');
    const editUsername = document.getElementById('editUsername');
    const editRole = document.getElementById('editRole');
    
    document.querySelectorAll('.edit-role-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const userId = this.dataset.userId;
            const username = this.dataset.username;
            const currentRole = this.dataset.currentRole;
            
            editUsername.textContent = username;
            editRole.value = currentRole || '';
            editRoleForm.action = `{% url 'payroll_admin:edit_user_role' 0 %}`.replace('0', userId);
            
            editRoleModal.show();
        });
    });
    
    // Delete user modal
    const deleteUserModal = new bootstrap.Modal(document.getElementById('deleteUserModal'));
    const deleteUserForm = document.getElementById('deleteUserForm');
    const deleteUsername = document.getElementById('deleteUsername');
    
    document.querySelectorAll('.delete-user-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const userId = this.dataset.userId;
            const username = this.dataset.username;
            
            deleteUsername.textContent = username;
            deleteUserForm.action = `{% url 'payroll_admin:delete_user' 0 %}`.replace('0', userId);
            
            deleteUserModal.show();
        });
    });
});
</script>
{% endblock %}
