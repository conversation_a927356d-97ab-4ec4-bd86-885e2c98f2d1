{% extends "admin/base.html" %}
{% load static %}

{% block title %}{% if subtitle %}{{ subtitle }} | {% endif %}{{ title }} | System Administration{% endblock %}

{% block branding %}
<div class="branding-container" style="padding: 10px 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
    <h1 id="site-name" style="margin: 0; font-size: 1.5rem; font-weight: 600;">
        <i class="bi bi-shield-lock me-2"></i>
        System Administration
    </h1>
    <p style="margin: 0; opacity: 0.8; font-size: 0.9rem;">Payroll Management System</p>
</div>
{% endblock %}

{% block nav-global %}{% endblock %}

{% block extrahead %}
{{ block.super }}
<!-- Bootstrap Icons -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

<style>
    /* Custom admin styling */
    #header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
    }
    
    #header a:link, #header a:visited {
        color: white !important;
    }
    
    #header a:hover {
        color: #f0f0f0 !important;
    }
    
    .module h2, .module caption, .inline-group h2 {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
    }
    
    .button, input[type=submit], input[type=button], .submit-row input, a.button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border: none !important;
        color: white !important;
    }
    
    .button:hover, input[type=submit]:hover, input[type=button]:hover, .submit-row input:hover, a.button:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
    }
    
    .breadcrumbs {
        background: #f8f9fa !important;
        border-bottom: 1px solid #dee2e6 !important;
    }
    
    .breadcrumbs a {
        color: #667eea !important;
    }
    
    /* Hide Django branding */
    #footer {
        display: none !important;
    }
    
    /* Custom footer */
    .custom-footer {
        background: #f8f9fa;
        border-top: 1px solid #dee2e6;
        padding: 15px 20px;
        text-align: center;
        color: #6c757d;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block footer %}
<div class="custom-footer">
    <p style="margin: 0;">
        <i class="bi bi-shield-check me-1"></i>
        Secure Payroll Management System
        <span class="mx-2">|</span>
        <a href="{% url 'core:admin_dashboard' %}" style="color: #667eea; text-decoration: none;">
            <i class="bi bi-speedometer2 me-1"></i>
            Enhanced Dashboard
        </a>
    </p>
</div>
{% endblock %}
