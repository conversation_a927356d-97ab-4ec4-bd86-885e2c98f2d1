<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Login - Kenyan Payroll Management System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --kenya-green: #006600;
            --kenya-red: #cc0000;
            --kenya-black: #000000;
            --kenya-white: #ffffff;
        }
        
        body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        
        .login-container {
            max-width: 450px;
            margin: 0 auto;
            padding: 1rem;
        }

        @media (max-width: 575.98px) {
            .login-container {
                max-width: 100%;
                padding: 0.5rem;
            }

            .login-card {
                margin: 0;
            }

            .login-header {
                padding: 1.5rem 1rem;
            }

            .login-body {
                padding: 1.5rem 1rem;
            }
        }
        
        .login-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .login-header {
            background: linear-gradient(135deg, var(--kenya-green), #1e7e34);
            color: white;
            padding: 2.5rem 2rem;
            text-align: center;
        }
        
        .login-body {
            padding: 2.5rem 2rem;
        }

        /* Additional security styles */
        input[type="password"]:not(:focus) {
            text-security: disc;
            -webkit-text-security: disc;
            -moz-text-security: disc;
        }

        /* Hide from password managers */
        input:-webkit-autofill,
        input:-webkit-autofill:hover,
        input:-webkit-autofill:focus,
        input:-webkit-autofill:active {
            -webkit-box-shadow: 0 0 0 30px white inset !important;
            -webkit-text-fill-color: #000 !important;
            transition: background-color 5000s ease-in-out 0s;
        }

        /* Prevent selection of password field */
        .form-control[type="password"] {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .form-control[type="password"]:focus {
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--kenya-green);
            box-shadow: 0 0 0 0.2rem rgba(0, 102, 0, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, var(--kenya-green), #1e7e34);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn-login:hover {
            background: linear-gradient(135deg, #1e7e34, var(--kenya-green));
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,102,0,0.3);
        }
        
        .kenya-flag {
            background: linear-gradient(to bottom, 
                var(--kenya-black) 0%, var(--kenya-black) 25%,
                var(--kenya-red) 25%, var(--kenya-red) 50%,
                var(--kenya-green) 50%, var(--kenya-green) 75%,
                var(--kenya-white) 75%, var(--kenya-white) 100%);
            height: 4px;
            width: 100%;
            margin: 1rem 0;
        }
        
        .security-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <i class="bi bi-shield-lock fs-1 mb-3 pulse"></i>
                    <h2 class="mb-2">System Login</h2>
                    <p class="mb-0 opacity-75">Kenyan Payroll Management System</p>
                    <div class="kenya-flag"></div>
                </div>
                
                <div class="login-body">
                    <div class="security-notice">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>Secure Access Required:</strong> This system contains sensitive payroll data.
                        All login attempts are monitored and logged.
                    </div>
                    
                    {% if form.errors %}
                        <div class="alert alert-danger">
                            <i class="bi bi-x-circle me-2"></i>
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    {{ error }}
                                {% endfor %}
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }}">
                                <i class="bi bi-info-circle me-2"></i>
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <!-- Dummy fields to confuse password managers -->
                    <div style="display: none;">
                        <input type="text" name="fake_username" autocomplete="username">
                        <input type="password" name="fake_password" autocomplete="current-password">
                    </div>

                    <form method="post" autocomplete="off" novalidate>
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="username" class="form-label fw-semibold">
                                <i class="bi bi-person me-2"></i>Username
                            </label>
                            <input type="text"
                                   class="form-control"
                                   id="username"
                                   name="username"
                                   placeholder="Enter your username"
                                   autocomplete="off"
                                   autocorrect="off"
                                   autocapitalize="off"
                                   spellcheck="false"
                                   data-lpignore="true"
                                   data-1p-ignore="true"
                                   data-bwignore="true"
                                   data-dashlane-rid="false"
                                   readonly
                                   onfocus="this.removeAttribute('readonly');"
                                   required>
                        </div>
                        
                        <div class="mb-4">
                            <label for="password" class="form-label fw-semibold">
                                <i class="bi bi-lock me-2"></i>Password
                            </label>
                            <input type="password"
                                   class="form-control"
                                   id="password"
                                   name="password"
                                   placeholder="Enter your password"
                                   autocomplete="new-password"
                                   autocorrect="off"
                                   autocapitalize="off"
                                   spellcheck="false"
                                   data-lpignore="true"
                                   data-form-type="other"
                                   data-1p-ignore="true"
                                   data-bwignore="true"
                                   data-dashlane-rid="false"
                                   data-lastpass-icon-root="false"
                                   readonly
                                   onfocus="this.removeAttribute('readonly');"
                                   required>
                        </div>
                        
                        <button type="submit" class="btn btn-login">
                            <i class="bi bi-box-arrow-in-right me-2"></i>
                            Access Dashboard
                        </button>
                    </form>
                    
                    <div class="text-center mt-4">
                        <a href="/" class="text-muted text-decoration-none me-3">
                            <i class="bi bi-arrow-left me-1"></i>
                            Back to Landing Page
                        </a>
                        <span class="text-muted">|</span>
                        <a href="/admin/" class="text-muted text-decoration-none ms-3">
                            <i class="bi bi-gear me-1"></i>
                            System Admin
                        </a>
                    </div>
                    
                    <div class="mt-4 pt-3 border-top">
                        <div class="row text-center">
                            <div class="col-4">
                                <i class="bi bi-shield-check text-success fs-4"></i>
                                <div class="small text-muted mt-1">Secure</div>
                            </div>
                            <div class="col-4">
                                <i class="bi bi-geo-alt text-primary fs-4"></i>
                                <div class="small text-muted mt-1">Kenya</div>
                            </div>
                            <div class="col-4">
                                <i class="bi bi-people text-info fs-4"></i>
                                <div class="small text-muted mt-1">All Users</div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                <i class="bi bi-info-circle me-1"></i>
                                Admins and regular users both access the main dashboard
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <div class="alert alert-warning alert-sm mb-3">
                    <i class="bi bi-shield-exclamation me-2"></i>
                    <strong>Security Notice:</strong> Password saving is disabled for security.
                    <br>
                    <small>
                        If your browser previously saved passwords,
                        <a href="/clear-passwords/" target="_blank" class="alert-link">
                            click here for instructions to clear them
                        </a>
                    </small>
                </div>
                <p class="text-muted small">
                    <i class="bi bi-info-circle me-1"></i>
                    Need help? Contact your system administrator
                </p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-focus username field
        document.addEventListener('DOMContentLoaded', function() {
            // More aggressive password saving prevention
            const passwordField = document.getElementById('{{ form.password.id_for_label }}');
            const usernameField = document.getElementById('{{ form.username.id_for_label }}');
            const loginForm = document.querySelector('form');

            // Focus username field after a delay to avoid autofill
            setTimeout(function() {
                if (usernameField) {
                    usernameField.focus();
                }
            }, 100);

            // Aggressive password manager prevention
            if (passwordField && usernameField) {
                // Change field names dynamically to confuse password managers
                const timestamp = Date.now();
                passwordField.name = 'pwd_' + timestamp;
                usernameField.name = 'usr_' + timestamp;

                // Add hidden input with original names for form submission
                const hiddenUsername = document.createElement('input');
                hiddenUsername.type = 'hidden';
                hiddenUsername.name = '{{ form.username.name }}';
                hiddenUsername.id = 'hidden_username';

                const hiddenPassword = document.createElement('input');
                hiddenPassword.type = 'hidden';
                hiddenPassword.name = '{{ form.password.name }}';
                hiddenPassword.id = 'hidden_password';

                loginForm.appendChild(hiddenUsername);
                loginForm.appendChild(hiddenPassword);

                // Copy values to hidden fields on input
                usernameField.addEventListener('input', function() {
                    hiddenUsername.value = this.value;
                });

                passwordField.addEventListener('input', function() {
                    hiddenPassword.value = this.value;
                });

                // Clear visible fields before form submission
                loginForm.addEventListener('submit', function() {
                    hiddenUsername.value = usernameField.value;
                    hiddenPassword.value = passwordField.value;

                    // Clear visible fields
                    setTimeout(function() {
                        usernameField.value = '';
                        passwordField.value = '';
                    }, 10);
                });

                // Continuously clear fields when not focused
                setInterval(function() {
                    if (document.activeElement !== passwordField && document.activeElement !== usernameField) {
                        if (passwordField.value && document.activeElement.tagName !== 'BUTTON') {
                            // Only clear if not submitting
                            if (!loginForm.classList.contains('submitting')) {
                                passwordField.value = '';
                            }
                        }
                    }
                }, 2000);

                // Mark form as submitting
                loginForm.addEventListener('submit', function() {
                    loginForm.classList.add('submitting');
                });

                // Clear fields on various events
                const clearEvents = ['blur', 'focusout'];
                clearEvents.forEach(function(event) {
                    passwordField.addEventListener(event, function() {
                        setTimeout(function() {
                            if (document.activeElement !== passwordField &&
                                document.activeElement !== usernameField &&
                                document.activeElement.type !== 'submit') {
                                passwordField.value = '';
                            }
                        }, 1000);
                    });
                });

                // Disable autocomplete more aggressively
                passwordField.setAttribute('autocomplete', 'new-password');
                usernameField.setAttribute('autocomplete', 'off');

                // Change input types periodically to confuse browsers
                let isPasswordVisible = false;
                passwordField.addEventListener('focus', function() {
                    if (!isPasswordVisible) {
                        this.type = 'text';
                        setTimeout(() => {
                            this.type = 'password';
                        }, 50);
                    }
                });
            }

            // Clear form data on page unload
            window.addEventListener('beforeunload', function() {
                if (passwordField) passwordField.value = '';
                if (usernameField) usernameField.value = '';
            });

            // Disable right-click context menu on password field
            if (passwordField) {
                passwordField.addEventListener('contextmenu', function(e) {
                    e.preventDefault();
                    return false;
                });
            }

            // Disable autocomplete programmatically
            if (loginForm) {
                loginForm.setAttribute('autocomplete', 'off');
            }

            // Clear clipboard after password input (security measure)
            if (passwordField) {
                passwordField.addEventListener('paste', function(e) {
                    setTimeout(function() {
                        if (navigator.clipboard && navigator.clipboard.writeText) {
                            navigator.clipboard.writeText('').catch(function() {
                                // Clipboard clearing failed, but continue
                            });
                        }
                    }, 100);
                });
            }
        });

        // Security messaging
        console.log('🔒 Kenyan Payroll Management System - Authorized Access Only');
        console.log('📊 All login attempts are monitored and logged');
        console.log('🛡️ Password saving is disabled for security');

        // Disable developer tools (basic deterrent)
        document.addEventListener('keydown', function(e) {
            // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
            if (e.keyCode === 123 ||
                (e.ctrlKey && e.shiftKey && (e.keyCode === 73 || e.keyCode === 74)) ||
                (e.ctrlKey && e.keyCode === 85)) {
                e.preventDefault();
                return false;
            }
        });

        // Disable right-click context menu
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            return false;
        });
    </script>
</body>
</html>
