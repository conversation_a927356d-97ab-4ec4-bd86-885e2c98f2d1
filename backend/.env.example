# Environment Configuration
NODE_ENV=development

# Server Configuration
PORT=5000
FRONTEND_URL=http://localhost:5173

# Database Configuration (PostgreSQL)
DATABASE_URL=postgresql://username:password@localhost:5432/kenyan_payroll
DB_HOST=localhost
DB_PORT=5432
DB_NAME=kenyan_payroll_dev
DB_USER=postgres
DB_PASSWORD=password
DB_TEST_NAME=kenyan_payroll_test

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# Admin User (for initial setup)
ADMIN_USERNAME=admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=PayrollAdmin2024!

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads

# Email Configuration (Optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-email-password
EMAIL_FROM=<EMAIL>

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Production Settings (for deployment)
# DATABASE_URL=postgresql://user:password@host:port/database
# FRONTEND_URL=https://your-domain.com
# JWT_SECRET=your-production-jwt-secret
