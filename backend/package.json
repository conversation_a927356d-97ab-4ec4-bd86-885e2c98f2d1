{"name": "kenyan-payroll-backend", "version": "1.0.0", "description": "Kenyan Payroll Management System - Node.js Backend API", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js", "setup": "npm run migrate && npm run seed", "test": "jest", "lint": "eslint .", "format": "prettier --write ."}, "keywords": ["payroll", "kenya", "shif", "nssf", "paye", "nodejs", "api", "postgresql"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "pg": "^8.11.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "xlsx": "^0.18.5", "pdfkit": "^0.13.0", "moment": "^2.29.4", "compression": "^1.7.4", "morgan": "^1.10.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "prettier": "^3.1.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}