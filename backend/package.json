{"name": "kenyan-payroll-backend", "version": "1.0.0", "description": "Kenyan Payroll Management System - Node.js Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js", "setup": "npm run migrate && npm run seed", "test": "jest", "lint": "eslint .", "format": "prettier --write ."}, "keywords": ["payroll", "kenya", "shif", "nssf", "paye", "nodejs", "api", "postgresql"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.17.1", "cors": "^2.8.5", "dotenv": "^10.0.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^8.5.1", "xlsx": "^0.17.0", "moment": "^2.29.4", "compression": "^1.7.4", "morgan": "^1.10.0", "uuid": "^8.3.2"}, "devDependencies": {"nodemon": "^2.0.15"}, "engines": {"node": ">=12.0.0", "npm": ">=6.0.0"}}