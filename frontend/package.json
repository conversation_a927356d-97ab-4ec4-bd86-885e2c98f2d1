{"name": "kenyan-payroll-frontend", "version": "1.0.0", "description": "Kenyan Payroll Management System - Vite Frontend", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write ."}, "keywords": ["payroll", "kenya", "vite", "frontend", "responsive"], "author": "Your Name", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.1.0", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-table": "^7.8.0", "xlsx": "^0.18.5", "jspdf": "^2.5.1", "html2canvas": "^1.4.1"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}