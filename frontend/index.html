<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Modern Kenyan Payroll Management System - SHIF, NSSF, PAYE compliant" />
    <meta name="keywords" content="payroll, kenya, shif, nssf, paye, salary, management" />
    <meta name="author" content="Kenyan Payroll System" />
    
    <!-- Mobile optimization -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="theme-color" content="#1e7e34" />
    
    <!-- Kindle optimization -->
    <meta name="format-detection" content="telephone=no" />
    <meta name="HandheldFriendly" content="true" />
    
    <!-- Preconnect to external resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <title>Kenyan Payroll Management System</title>
    
    <style>
      /* Loading screen styles */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-family: 'Inter', sans-serif;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 10px;
      }
      
      .loading-subtext {
        font-size: 14px;
        opacity: 0.8;
      }
      
      /* Hide loading screen when app loads */
      .app-loaded #loading-screen {
        display: none;
      }
      
      /* Prevent flash of unstyled content */
      #root {
        min-height: 100vh;
      }
      
      /* Custom scrollbar */
      ::-webkit-scrollbar {
        width: 8px;
      }
      
      ::-webkit-scrollbar-track {
        background: #f1f1f1;
      }
      
      ::-webkit-scrollbar-thumb {
        background: #1e7e34;
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: #155724;
      }
    </style>
  </head>
  <body>
    <!-- Loading screen -->
    <div id="loading-screen">
      <div class="loading-spinner"></div>
      <div class="loading-text">Kenyan Payroll System</div>
      <div class="loading-subtext">Loading your dashboard...</div>
    </div>
    
    <!-- React app root -->
    <div id="root"></div>
    
    <script type="module" src="/src/main.jsx"></script>
    
    <script>
      // Hide loading screen when page loads
      window.addEventListener('load', function() {
        setTimeout(function() {
          document.body.classList.add('app-loaded');
        }, 500);
      });
      
      // Service worker registration (for PWA capabilities)
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
          navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
              console.log('SW registered: ', registration);
            })
            .catch(function(registrationError) {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
  </body>
</html>
