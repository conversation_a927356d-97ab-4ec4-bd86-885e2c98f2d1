{"version": 2, "builds": [{"src": "payroll/wsgi.py", "use": "@vercel/python", "config": {"maxLambdaSize": "50mb", "runtime": "python3.9", "maxDuration": 30}}], "routes": [{"src": "/static/(.*)", "dest": "/static/$1"}, {"src": "/media/(.*)", "dest": "/media/$1"}, {"src": "/(.*)", "dest": "payroll/wsgi.py"}], "env": {"DJANGO_SETTINGS_MODULE": "payroll.settings.vercel", "PYTHONPATH": ".", "DEBUG": "False"}, "functions": {"payroll/wsgi.py": {"maxDuration": 30}}}