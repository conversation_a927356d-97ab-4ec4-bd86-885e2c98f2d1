# Heroku-specific requirements for Kenyan Payroll Management System
# Core Django framework
Django==4.2.7
django-environ==0.11.2

# Database
psycopg2-binary==2.9.9  # PostgreSQL adapter for Heroku
dj-database-url==2.1.0  # Database URL parsing

# Web server
gunicorn==21.2.0  # WSGI HTTP Server for Heroku
whitenoise==6.6.0  # Static file serving

# Date and time handling
python-dateutil==2.8.2
pytz==2023.3

# Excel processing (Python 3.11+ compatible)
openpyxl==3.1.2

# Security and CORS
django-cors-headers==4.3.1

# Environment variables
python-decouple==3.8

# Heroku-specific
django-heroku==0.3.1
